# Azure App Configuration Keys Verification Script
Write-Host "=== Azure App Configuration Keys Check ===" -ForegroundColor Green

# Read the appsettings.json file to get connection details
$appsettingsPath = "iRISHome/appsettings.json"
if (Test-Path $appsettingsPath) {
    $appsettings = Get-Content $appsettingsPath | ConvertFrom-Json
    $endpoint = $appsettings.AppConfig.Endpoint
    
    Write-Host ""
    Write-Host "Azure App Configuration Endpoint: $endpoint" -ForegroundColor Yellow
    
    # Check if Azure CLI is available
    try {
        $azVersion = az version 2>$null | ConvertFrom-Json
        if ($azVersion) {
            Write-Host "Azure CLI Version: $($azVersion.'azure-cli')" -ForegroundColor Green
            
            # Check if logged in
            $account = az account show 2>$null | ConvertFrom-Json
            if ($account) {
                Write-Host "Logged in as: $($account.user.name)" -ForegroundColor Green
                
                # Extract the App Configuration name from the endpoint
                $configName = ($endpoint -split '\.')[0] -replace 'https://', ''
                
                Write-Host ""
                Write-Host "Checking keys in Azure App Configuration: $configName" -ForegroundColor Yellow
                
                # List all configuration keys
                Write-Host ""
                Write-Host "=== ALL KEYS IN AZURE APP CONFIGURATION ===" -ForegroundColor Cyan
                try {
                    $allKeys = az appconfig kv list --name $configName --output json | ConvertFrom-Json
                    
                    if ($allKeys.Count -eq 0) {
                        Write-Host "No keys found in Azure App Configuration!" -ForegroundColor Red
                    } else {
                        Write-Host "Total keys found: $($allKeys.Count)" -ForegroundColor Green
                        
                        # Show all keys
                        foreach ($key in $allKeys) {
                            $keyName = $key.key
                            $hasValue = -not [string]::IsNullOrEmpty($key.value)
                            $valueStatus = if ($hasValue) { "HAS_VALUE" } else { "EMPTY/NULL" }
                            $color = if ($hasValue) { "Green" } else { "Red" }
                            Write-Host "  $keyName = $valueStatus" -ForegroundColor $color
                        }
                        
                        # Filter for Keycloak-related keys
                        Write-Host ""
                        Write-Host "=== KEYCLOAK-RELATED KEYS ===" -ForegroundColor Cyan
                        $keycloakKeys = $allKeys | Where-Object { $_.key -like "*Keycloak*" -or $_.key -like "*keycloak*" }
                        
                        if ($keycloakKeys.Count -eq 0) {
                            Write-Host "No Keycloak-related keys found!" -ForegroundColor Red
                            Write-Host ""
                            Write-Host "Expected keys that should be created:" -ForegroundColor Yellow
                            Write-Host "  KeycloakAuthentication:OpenIdConnect:ClientId" -ForegroundColor Cyan
                            Write-Host "  KeycloakAuthentication:OpenIdConnect:ClientSecret" -ForegroundColor Cyan
                            Write-Host "  KeycloakAuthentication:OpenIdConnect:Authority" -ForegroundColor Cyan
                        } else {
                            Write-Host "Found $($keycloakKeys.Count) Keycloak-related keys:" -ForegroundColor Green
                            foreach ($key in $keycloakKeys) {
                                $hasValue = -not [string]::IsNullOrEmpty($key.value)
                                $valueStatus = if ($hasValue) { "HAS_VALUE" } else { "EMPTY/NULL" }
                                $color = if ($hasValue) { "Green" } else { "Red" }
                                Write-Host "  $($key.key) = $valueStatus" -ForegroundColor $color
                            }
                        }
                        
                        # Show commands to create missing keys
                        Write-Host ""
                        Write-Host "=== COMMANDS TO CREATE MISSING KEYCLOAK KEYS ===" -ForegroundColor Yellow
                        Write-Host "Run these commands to create the required keys:" -ForegroundColor Cyan
                        Write-Host ""
                        Write-Host "az appconfig kv set --name $configName --key `"KeycloakAuthentication:OpenIdConnect:ClientId`" --value `"your_client_id`"" -ForegroundColor White
                        Write-Host "az appconfig kv set --name $configName --key `"KeycloakAuthentication:OpenIdConnect:ClientSecret`" --value `"your_client_secret`"" -ForegroundColor White
                        Write-Host "az appconfig kv set --name $configName --key `"KeycloakAuthentication:OpenIdConnect:Authority`" --value `"https://auth.mobileaspectshealth.org/realms/MAH9-MobileaspectsHealth`"" -ForegroundColor White
                    }
                } catch {
                    Write-Host "Error listing keys: $($_.Exception.Message)" -ForegroundColor Red
                    Write-Host "Make sure you have the 'App Configuration Data Reader' role on the Azure App Configuration resource" -ForegroundColor Yellow
                }
            } else {
                Write-Host "Not logged in to Azure CLI. Run 'az login' first." -ForegroundColor Red
            }
        }
    } catch {
        Write-Host "Azure CLI not found. Please install Azure CLI to check the keys." -ForegroundColor Red
        Write-Host "Download from: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli" -ForegroundColor Yellow
    }
} else {
    Write-Host "Error: appsettings.json not found at $appsettingsPath" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Check Complete ===" -ForegroundColor Green
