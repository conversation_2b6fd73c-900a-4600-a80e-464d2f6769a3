<!DOCTYPE html>
<html>
<head>
    <title>iRISHome Status</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .status-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .status-ok { color: #28a745; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
        .status-warning { color: #ffc107; font-weight: bold; }
        .status-info { color: #17a2b8; font-weight: bold; }
        h1 { color: #333; text-align: center; }
        h2 { color: #555; border-bottom: 2px solid #007bff; padding-bottom: 5px; }
        .key-value { margin: 8px 0; }
        .key { font-weight: bold; color: #333; display: inline-block; width: 200px; }
        .value { color: #666; }
        .timestamp { text-align: center; color: #888; font-size: 0.9em; margin-top: 20px; }
        .actions { text-align: center; margin: 20px 0; }
        .btn { padding: 10px 20px; margin: 5px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; display: inline-block; }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏥 iRISHome Application Status</h1>
        
        <div class="actions">
            <a href="/config-debug" class="btn">Configuration Debug</a>
            <a href="/ConfigurationDebug/TestKeycloakConnectivity" class="btn">Test Keycloak</a>
            <a href="/" class="btn">Home</a>
        </div>

        <div class="status-section">
            <h2>🔐 Authentication Status</h2>
            <div class="key-value">
                <span class="key">Keycloak Configured:</span>
                <span class="value @(ViewBag.Status.Authentication.KeycloakConfigured ? "status-ok" : "status-error")">
                    @(ViewBag.Status.Authentication.KeycloakConfigured ? "✓ Yes" : "✗ No")
                </span>
            </div>
            <div class="key-value">
                <span class="key">Keycloak Authority:</span>
                <span class="value">@(ViewBag.Status.Authentication.KeycloakAuthority ?? "Not Set")</span>
            </div>
            <div class="key-value">
                <span class="key">Azure App Config:</span>
                <span class="value @(ViewBag.Status.Authentication.AzureAppConfigConnected ? "status-ok" : "status-error")">
                    @(ViewBag.Status.Authentication.AzureAppConfigConnected ? "✓ Connected" : "✗ Not Connected")
                </span>
            </div>
        </div>

        <div class="status-section">
            <h2>🖥️ Environment Information</h2>
            <div class="key-value">
                <span class="key">Environment:</span>
                <span class="value">@(ViewBag.Status.Environment.AspNetCoreEnvironment ?? "Not Set")</span>
            </div>
            <div class="key-value">
                <span class="key">Machine Name:</span>
                <span class="value">@ViewBag.Status.Environment.MachineName</span>
            </div>
            <div class="key-value">
                <span class="key">User:</span>
                <span class="value">@ViewBag.Status.Environment.UserName</span>
            </div>
        </div>

        <div class="status-section">
            <h2>📋 Current Issues & Solutions</h2>
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 4px; margin: 10px 0;">
                <strong>⚠️ Known Issue:</strong> Keycloak server is returning 502 Bad Gateway
                <br><br>
                <strong>Solutions:</strong>
                <ul>
                    <li>Contact your Keycloak administrator to fix the server</li>
                    <li>Verify the Keycloak URL: <code>@ViewBag.Status.Authentication.KeycloakAuthority</code></li>
                    <li>The application will fall back to cookie authentication when Keycloak is unavailable</li>
                </ul>
            </div>
        </div>

        <div class="timestamp">
            Last updated: @ViewBag.Status.Timestamp.ToString("yyyy-MM-dd HH:mm:ss") UTC
        </div>
    </div>
</body>
</html>
