
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.11.35327.3
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "AuthLogin", "AuthLogin\AuthLogin.csproj", "{D1D32BDF-EE01-4612-AE6E-4E28F0B3B419}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "iRISDashboard", "iRISDashboard\iRISDashboard.csproj", "{70BA9D89-E5E2-4312-8F0C-54F95DFD7CDD}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "iRISHome", "iRISHome\iRISHome.csproj", "{0CB80015-1E48-4C44-87ED-7CE5C3D8820A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "iRISHomeWeb.AppHost", "iRISHomeWeb.AppHost\iRISHomeWeb.AppHost.csproj", "{3D90BC89-F3E9-B830-725E-7EF003E3DC8B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "iRISHomeWeb.ServiceDefaults", "iRISHomeWeb.ServiceDefaults\iRISHomeWeb.ServiceDefaults.csproj", "{CA04B8FF-C3C8-5A27-A529-A4B402D9D574}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{8EC462FD-D22E-90A8-E5CE-7E832BA40C5D}"
	ProjectSection(SolutionItems) = preProject
		Directory.Build.props = Directory.Build.props
		Directory.Packages.props = Directory.Packages.props
		NuGet.config = NuGet.config
		packages.lock.json = packages.lock.json
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{D1D32BDF-EE01-4612-AE6E-4E28F0B3B419}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D1D32BDF-EE01-4612-AE6E-4E28F0B3B419}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D1D32BDF-EE01-4612-AE6E-4E28F0B3B419}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D1D32BDF-EE01-4612-AE6E-4E28F0B3B419}.Release|Any CPU.Build.0 = Release|Any CPU
		{70BA9D89-E5E2-4312-8F0C-54F95DFD7CDD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{70BA9D89-E5E2-4312-8F0C-54F95DFD7CDD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{70BA9D89-E5E2-4312-8F0C-54F95DFD7CDD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{70BA9D89-E5E2-4312-8F0C-54F95DFD7CDD}.Release|Any CPU.Build.0 = Release|Any CPU
		{0CB80015-1E48-4C44-87ED-7CE5C3D8820A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0CB80015-1E48-4C44-87ED-7CE5C3D8820A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0CB80015-1E48-4C44-87ED-7CE5C3D8820A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0CB80015-1E48-4C44-87ED-7CE5C3D8820A}.Release|Any CPU.Build.0 = Release|Any CPU
		{3D90BC89-F3E9-B830-725E-7EF003E3DC8B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3D90BC89-F3E9-B830-725E-7EF003E3DC8B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3D90BC89-F3E9-B830-725E-7EF003E3DC8B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3D90BC89-F3E9-B830-725E-7EF003E3DC8B}.Release|Any CPU.Build.0 = Release|Any CPU
		{CA04B8FF-C3C8-5A27-A529-A4B402D9D574}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CA04B8FF-C3C8-5A27-A529-A4B402D9D574}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CA04B8FF-C3C8-5A27-A529-A4B402D9D574}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CA04B8FF-C3C8-5A27-A529-A4B402D9D574}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {9A738A46-1120-4A16-B56D-55AD5B908664}
	EndGlobalSection
EndGlobal
