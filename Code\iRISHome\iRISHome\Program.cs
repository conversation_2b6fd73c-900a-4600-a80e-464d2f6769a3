using Azure.Identity; // Add this namespace for TokenCredential support
using iRISHome.DbFunctions;
using iRISHome.Models;
using JSONLogger;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.Azure.AppConfiguration.AspNetCore;
using Microsoft.Extensions.Configuration.AzureAppConfiguration;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Hosting;
using Microsoft.FeatureManagement;

var builder = WebApplication.CreateBuilder(args);
// Add Azure App Configuration as a configuration source
//var configuration = builder.Configuration;
var connectionString = builder.Configuration["AppConfig:ConnectionString"];
var endpoint = builder.Configuration["AppConfig:Endpoint"];
// Add Azure App Configuration before using its values
if (!string.IsNullOrEmpty(connectionString))
{
    builder.Configuration.AddAzureAppConfiguration(options =>
    {
        options.Connect(connectionString)
              .Select(KeyFilter.Any)
              .Select("KeycloakAuthentication:*")
              .ConfigureRefresh(refresh =>
              {
                  refresh.Register("KeycloakAuthentication:*", refreshAll: true)
                         .SetCacheExpiration(TimeSpan.FromSeconds(30));
              });
    });
}
else if (!string.IsNullOrEmpty(endpoint))
{
    builder.Configuration.AddAzureAppConfiguration(options =>
    {
        options.Connect(new Uri(endpoint), new Azure.Identity.DefaultAzureCredential())
              .Select("KeycloakAuthentication:*");
    });
}

builder.AddServiceDefaults();
builder.Services.AddAzureAppConfiguration();
// Register DBObjects as a scoped service
builder.Services.AddScoped<DBObjects>();

builder.Services.AddControllersWithViews();
builder.Services.TryAddSingleton<IHttpContextAccessor, HttpContextAccessor>();
builder.Services.AddSession();
builder.Services.AddKendo();


// Get the updated configuration after adding Azure App Configuration
//var configuration = builder.Configuration;
builder.Services.AddAuthentication(options =>
{
    options.DefaultScheme = CookieAuthenticationDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = OpenIdConnectDefaults.AuthenticationScheme;
})
.AddCookie()
.AddOpenIdConnect(options =>
{
    options.ClientId = builder.Configuration["KeycloakAuthentication:OpenIdConnect:ClientId"];
    options.ClientSecret = builder.Configuration["KeycloakAuthentication:OpenIdConnect:ClientSecret"];
    options.Authority = builder.Configuration["KeycloakAuthentication:OpenIdConnect:Authority"];
    options.ResponseType = "code";
    //options.SaveTokens = true;
    options.Scope.Add("profile");
    options.Scope.Add("email");
    options.Scope.Add("openid");
    //options.BackchannelHttpHandler = new HttpClientHandler
    //{
    //    ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true
    //};
});


var app = builder.Build();
// Add after var app = builder.Build();
if (!string.IsNullOrEmpty(connectionString) || !string.IsNullOrEmpty(endpoint))
{
    app.UseAzureAppConfiguration();
}

app.MapDefaultEndpoints();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())  
{
    app.UseExceptionHandler("/Home/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}
app.UseSession();

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();
app.UseAuthentication();
app.UseAuthorization();

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=AuthLogin}/{action=KeycloakValidation}/{id?}");

app.Run();

// Note: The provided code block is a NuGet package installation command and cannot be directly incorporated into the C# file. 
// To install the package, run the command `Install-Package Telerik.UI.for.AspNet.Core -Version 2024.1.130` in the NuGet Package Manager Console.
