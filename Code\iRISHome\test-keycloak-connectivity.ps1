# Test Keycloak Connectivity Script
Write-Host "=== Testing Keycloak Connectivity ===" -ForegroundColor Green

$keycloakUrl = "https://auth.mobileaspectshealth.org/realms/MAH9-MobileaspectsHealth/.well-known/openid-configuration"

Write-Host ""
Write-Host "Testing connectivity to: $keycloakUrl" -ForegroundColor Yellow

try {
    # Test basic connectivity
    Write-Host "1. Testing basic connectivity..." -ForegroundColor Cyan
    $response = Invoke-WebRequest -Uri $keycloakUrl -UseBasicParsing -TimeoutSec 30
    Write-Host "   Success: Status Code $($response.StatusCode)" -ForegroundColor Green
    
    # Parse and display some configuration
    $config = $response.Content | ConvertFrom-Json
    Write-Host "   Issuer: $($config.issuer)" -ForegroundColor Green
    Write-Host "   Authorization Endpoint: $($config.authorization_endpoint)" -ForegroundColor Green
    Write-Host "   Token Endpoint: $($config.token_endpoint)" -ForegroundColor Green
    
} catch {
    Write-Host "   Failed: $($_.Exception.Message)" -ForegroundColor Red
    
    if ($_.Exception.InnerException) {
        Write-Host "   Inner Exception: $($_.Exception.InnerException.Message)" -ForegroundColor Red
    }
    
    # Try with certificate validation disabled
    Write-Host ""
    Write-Host "2. Testing with certificate validation disabled..." -ForegroundColor Cyan
    try {
        # For PowerShell 6+ (Core)
        if ($PSVersionTable.PSVersion.Major -ge 6) {
            $response = Invoke-WebRequest -Uri $keycloakUrl -UseBasicParsing -SkipCertificateCheck -TimeoutSec 30
            Write-Host "   Success with SkipCertificateCheck: Status Code $($response.StatusCode)" -ForegroundColor Green
        } else {
            # For Windows PowerShell 5.1
            [System.Net.ServicePointManager]::ServerCertificateValidationCallback = {$true}
            $response = Invoke-WebRequest -Uri $keycloakUrl -UseBasicParsing -TimeoutSec 30
            Write-Host "   Success with certificate validation disabled: Status Code $($response.StatusCode)" -ForegroundColor Green
            [System.Net.ServicePointManager]::ServerCertificateValidationCallback = $null
        }
    } catch {
        Write-Host "   Still failed: $($_.Exception.Message)" -ForegroundColor Red
        
        # Test DNS resolution
        Write-Host ""
        Write-Host "3. Testing DNS resolution..." -ForegroundColor Cyan
        try {
            $dnsResult = Resolve-DnsName "auth.mobileaspectshealth.org"
            Write-Host "   DNS Resolution successful:" -ForegroundColor Green
            foreach ($record in $dnsResult) {
                Write-Host "     $($record.Name) -> $($record.IPAddress)" -ForegroundColor Green
            }
        } catch {
            Write-Host "   DNS Resolution failed: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        # Test basic TCP connectivity
        Write-Host ""
        Write-Host "4. Testing TCP connectivity to port 443..." -ForegroundColor Cyan
        try {
            $tcpClient = New-Object System.Net.Sockets.TcpClient
            $tcpClient.Connect("auth.mobileaspectshealth.org", 443)
            if ($tcpClient.Connected) {
                Write-Host "   TCP connection successful" -ForegroundColor Green
                $tcpClient.Close()
            }
        } catch {
            Write-Host "   TCP connection failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "=== Test Complete ===" -ForegroundColor Green
