using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using System.Text.Json;

namespace iRISHome.Controllers
{
    public class ConfigurationDebugController : Controller
    {
        private readonly IConfiguration _configuration;

        public ConfigurationDebugController(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        /// <summary>
        /// Debug endpoint to check Azure App Configuration values
        /// Access via: /ConfigurationDebug/CheckKeycloakConfig
        /// </summary>
        public IActionResult CheckKeycloakConfig()
        {
            var configInfo = new
            {
                AzureAppConfig = new
                {
                    ConnectionString = !string.IsNullOrEmpty(_configuration["AppConfig:ConnectionString"]) ? "SET" : "NOT SET",
                    Endpoint = _configuration["AppConfig:Endpoint"] ?? "NOT SET"
                },
                KeycloakConfig = new
                {
                    ClientId = _configuration["KeycloakAuthentication:OpenIdConnect:ClientId"] ?? "NOT SET",
                    ClientSecret = !string.IsNullOrEmpty(_configuration["KeycloakAuthentication:OpenIdConnect:ClientSecret"]) ? "SET" : "NOT SET",
                    Authority = _configuration["KeycloakAuthentication:OpenIdConnect:Authority"] ?? "NOT SET"
                },
                AllKeycloakKeys = GetAllKeycloakKeys(),
                ConfigurationProviders = GetConfigurationProviders()
            };

            return Json(configInfo, new JsonSerializerOptions { WriteIndented = true });
        }

        private Dictionary<string, string> GetAllKeycloakKeys()
        {
            var keycloakKeys = new Dictionary<string, string>();
            
            // Get all configuration keys that start with "KeycloakAuthentication"
            var allKeys = _configuration.AsEnumerable()
                .Where(kvp => kvp.Key.StartsWith("KeycloakAuthentication", StringComparison.OrdinalIgnoreCase))
                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value ?? "NULL");

            return allKeys;
        }

        private List<string> GetConfigurationProviders()
        {
            var providers = new List<string>();
            
            if (_configuration is IConfigurationRoot configRoot)
            {
                foreach (var provider in configRoot.Providers)
                {
                    providers.Add(provider.GetType().Name);
                }
            }

            return providers;
        }

        /// <summary>
        /// Test Azure App Configuration connectivity
        /// Access via: /ConfigurationDebug/TestAzureAppConfig
        /// </summary>
        public IActionResult TestAzureAppConfig()
        {
            var result = new
            {
                ConnectionString = _configuration["AppConfig:ConnectionString"],
                Endpoint = _configuration["AppConfig:Endpoint"],
                TestResult = "Check console output for connection details"
            };

            return Json(result, new JsonSerializerOptions { WriteIndented = true });
        }

        /// <summary>
        /// Test Keycloak server connectivity
        /// Access via: /ConfigurationDebug/TestKeycloakConnectivity
        /// </summary>
        public async Task<IActionResult> TestKeycloakConnectivity()
        {
            var authority = _configuration["KeycloakAuthentication:OpenIdConnect:Authority"];

            if (string.IsNullOrEmpty(authority))
            {
                return Json(new { Error = "Keycloak Authority not configured" });
            }

            var wellKnownUrl = $"{authority}/.well-known/openid-configuration";

            try
            {
                using var httpClient = new HttpClient();
                httpClient.Timeout = TimeSpan.FromSeconds(10);

                var response = await httpClient.GetAsync(wellKnownUrl);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var config = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(content);

                    return Json(new
                    {
                        Status = "Success",
                        StatusCode = (int)response.StatusCode,
                        Url = wellKnownUrl,
                        Issuer = config?.GetValueOrDefault("issuer"),
                        AuthorizationEndpoint = config?.GetValueOrDefault("authorization_endpoint"),
                        TokenEndpoint = config?.GetValueOrDefault("token_endpoint")
                    }, new JsonSerializerOptions { WriteIndented = true });
                }
                else
                {
                    return Json(new
                    {
                        Status = "Failed",
                        StatusCode = (int)response.StatusCode,
                        ReasonPhrase = response.ReasonPhrase,
                        Url = wellKnownUrl
                    }, new JsonSerializerOptions { WriteIndented = true });
                }
            }
            catch (Exception ex)
            {
                return Json(new
                {
                    Status = "Error",
                    Message = ex.Message,
                    Url = wellKnownUrl,
                    InnerException = ex.InnerException?.Message
                }, new JsonSerializerOptions { WriteIndented = true });
            }
        }
    }
}
