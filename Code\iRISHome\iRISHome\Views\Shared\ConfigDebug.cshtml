<!DOCTYPE html>
<html>
<head>
    <title>Configuration Debug</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .config-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 5px; }
        .config-key { font-weight: bold; color: #333; }
        .config-value { color: #666; margin-left: 20px; }
        .status-ok { color: green; }
        .status-error { color: red; }
        .status-warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Azure App Configuration & Keycloak Debug</h1>
    
    <div class="config-section">
        <h2>Quick Actions</h2>
        <button onclick="checkConfig()">Check Configuration</button>
        <button onclick="testAzureConfig()">Test Azure App Config</button>
        <button onclick="testKeycloakConnectivity()">Test Keycloak Connectivity</button>
        <button onclick="refreshPage()">Refresh</button>
    </div>

    <div class="config-section">
        <h2>Configuration Status</h2>
        <div id="configStatus">Click "Check Configuration" to load status...</div>
    </div>

    <div class="config-section">
        <h2>Azure App Configuration Test</h2>
        <div id="azureConfigTest">Click "Test Azure App Config" to test connectivity...</div>
    </div>

    <div class="config-section">
        <h2>Keycloak Connectivity Test</h2>
        <div id="keycloakConnectivityTest">Click "Test Keycloak Connectivity" to test server availability...</div>
    </div>

    <script>
        async function checkConfig() {
            try {
                const response = await fetch('/ConfigurationDebug/CheckKeycloakConfig');
                const data = await response.json();
                
                let html = '<h3>Configuration Details</h3>';
                html += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                
                // Add status indicators
                html += '<h3>Status Summary</h3>';
                html += '<div class="config-key">Azure App Config Connection: ';
                html += data.AzureAppConfig.ConnectionString === 'SET' ? 
                    '<span class="status-ok">✓ Connected</span>' : 
                    '<span class="status-error">✗ Not Connected</span>';
                html += '</div>';
                
                html += '<div class="config-key">Keycloak ClientId: ';
                html += data.KeycloakConfig.ClientId !== 'NOT SET' ? 
                    '<span class="status-ok">✓ Set</span>' : 
                    '<span class="status-error">✗ Not Set</span>';
                html += '</div>';
                
                html += '<div class="config-key">Keycloak Authority: ';
                html += data.KeycloakConfig.Authority !== 'NOT SET' ? 
                    '<span class="status-ok">✓ Set</span>' : 
                    '<span class="status-error">✗ Not Set</span>';
                html += '</div>';
                
                document.getElementById('configStatus').innerHTML = html;
            } catch (error) {
                document.getElementById('configStatus').innerHTML = 
                    '<span class="status-error">Error: ' + error.message + '</span>';
            }
        }

        async function testAzureConfig() {
            try {
                const response = await fetch('/ConfigurationDebug/TestAzureAppConfig');
                const data = await response.json();

                let html = '<h3>Azure App Configuration Test</h3>';
                html += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';

                document.getElementById('azureConfigTest').innerHTML = html;
            } catch (error) {
                document.getElementById('azureConfigTest').innerHTML =
                    '<span class="status-error">Error: ' + error.message + '</span>';
            }
        }

        async function testKeycloakConnectivity() {
            document.getElementById('keycloakConnectivityTest').innerHTML =
                '<span class="status-warning">Testing Keycloak connectivity...</span>';

            try {
                const response = await fetch('/ConfigurationDebug/TestKeycloakConnectivity');
                const data = await response.json();

                let html = '<h3>Keycloak Connectivity Test</h3>';
                html += '<pre>' + JSON.stringify(data, null, 2) + '</pre>';

                // Add status indicator
                if (data.Status === 'Success') {
                    html += '<div class="status-ok">✓ Keycloak server is reachable and responding</div>';
                } else if (data.Status === 'Failed') {
                    html += '<div class="status-error">✗ Keycloak server returned error: ' + data.StatusCode + ' ' + data.ReasonPhrase + '</div>';
                } else {
                    html += '<div class="status-error">✗ Keycloak server connectivity failed: ' + data.Message + '</div>';
                }

                document.getElementById('keycloakConnectivityTest').innerHTML = html;
            } catch (error) {
                document.getElementById('keycloakConnectivityTest').innerHTML =
                    '<span class="status-error">Error: ' + error.message + '</span>';
            }
        }

        function refreshPage() {
            location.reload();
        }

        // Auto-load configuration on page load
        window.onload = function() {
            checkConfig();
        };
    </script>
</body>
</html>
