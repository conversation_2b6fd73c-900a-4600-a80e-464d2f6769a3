# Fix Azure App Configuration Labels Script
Write-Host "=== Fixing Azure App Configuration Labels ===" -ForegroundColor Green

$configName = "mah9-all-all0-cfg-d01"

Write-Host ""
Write-Host "Current keys with labels:" -ForegroundColor Yellow
az appconfig kv list --name $configName --output table

Write-Host ""
Write-Host "Creating keys without labels..." -ForegroundColor Yellow

# Get the current values
$clientIdResult = az appconfig kv show --name $configName --key "KeycloakAuthentication:OpenIdConnect:ClientId" --label "ClientID" --output json 2>$null | ConvertFrom-Json
$clientSecretResult = az appconfig kv show --name $configName --key "KeycloakAuthentication:OpenIdConnect:ClientSecret" --label "ClientSecret" --output json 2>$null | ConvertFrom-Json
$authorityResult = az appconfig kv show --name $configName --key "KeycloakAuthentication:OpenIdConnect:Authority" --label "Authority" --output json 2>$null | ConvertFrom-Json

if ($clientIdResult) {
    Write-Host "Creating ClientId without label..." -ForegroundColor Cyan
    az appconfig kv set --name $configName --key "KeycloakAuthentication:OpenIdConnect:ClientId" --value $clientIdResult.value --yes
    Write-Host "  Created: KeycloakAuthentication:OpenIdConnect:ClientId = $($clientIdResult.value)" -ForegroundColor Green
}

if ($clientSecretResult) {
    Write-Host "Creating ClientSecret without label..." -ForegroundColor Cyan
    az appconfig kv set --name $configName --key "KeycloakAuthentication:OpenIdConnect:ClientSecret" --value $clientSecretResult.value --yes
    Write-Host "  Created: KeycloakAuthentication:OpenIdConnect:ClientSecret = [HIDDEN]" -ForegroundColor Green
}

if ($authorityResult) {
    Write-Host "Creating Authority without label..." -ForegroundColor Cyan
    az appconfig kv set --name $configName --key "KeycloakAuthentication:OpenIdConnect:Authority" --value $authorityResult.value --yes
    Write-Host "  Created: KeycloakAuthentication:OpenIdConnect:Authority = $($authorityResult.value)" -ForegroundColor Green
}

Write-Host ""
Write-Host "Updated keys list:" -ForegroundColor Yellow
az appconfig kv list --name $configName --output table

Write-Host ""
Write-Host "=== Fix Complete ===" -ForegroundColor Green
Write-Host "The application should now be able to retrieve the Keycloak values from Azure App Configuration" -ForegroundColor Cyan
