# Azure App Configuration Verification Script
Write-Host "=== Azure App Configuration Verification ===" -ForegroundColor Green

# Read the appsettings.json file
$appsettingsPath = "iRISHome/appsettings.json"
if (Test-Path $appsettingsPath) {
    $appsettings = Get-Content $appsettingsPath | ConvertFrom-Json

    Write-Host ""
    Write-Host "1. Local Configuration Check:" -ForegroundColor Yellow
    $connStringStatus = if($appsettings.AppConfig.ConnectionString) {'SET'} else {'NOT SET'}
    $connStringColor = if($appsettings.AppConfig.ConnectionString) {'Green'} else {'Red'}
    Write-Host "   Connection String: $connStringStatus" -ForegroundColor $connStringColor
    Write-Host "   Endpoint: $($appsettings.AppConfig.Endpoint)" -ForegroundColor Green

    if ($appsettings.KeycloakAuthentication) {
        Write-Host ""
        Write-Host "2. Local Keycloak Fallback Configuration:" -ForegroundColor Yellow
        Write-Host "   ClientId: $($appsettings.KeycloakAuthentication.OpenIdConnect.ClientId)" -ForegroundColor Green
        Write-Host "   Authority: $($appsettings.KeycloakAuthentication.OpenIdConnect.Authority)" -ForegroundColor Green
        $secretStatus = if($appsettings.KeycloakAuthentication.OpenIdConnect.ClientSecret) {'SET'} else {'NOT SET'}
        $secretColor = if($appsettings.KeycloakAuthentication.OpenIdConnect.ClientSecret) {'Green'} else {'Red'}
        Write-Host "   ClientSecret: $secretStatus" -ForegroundColor $secretColor
    }
} else {
    Write-Host "Error: appsettings.json not found at $appsettingsPath" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "3. Recommended Actions:" -ForegroundColor Yellow
Write-Host "   1. Run your application and visit: http://localhost:5000/config-debug" -ForegroundColor Cyan
Write-Host "   2. Check the console output when starting the application" -ForegroundColor Cyan
Write-Host "   3. Verify Azure App Configuration contains keys starting with 'KeycloakAuthentication:'" -ForegroundColor Cyan
Write-Host "   4. Ensure your Azure credentials have read access to the App Configuration" -ForegroundColor Cyan

Write-Host ""
Write-Host "4. Azure App Configuration Keys to Verify:" -ForegroundColor Yellow
Write-Host "   - KeycloakAuthentication:OpenIdConnect:ClientId" -ForegroundColor Cyan
Write-Host "   - KeycloakAuthentication:OpenIdConnect:ClientSecret" -ForegroundColor Cyan
Write-Host "   - KeycloakAuthentication:OpenIdConnect:Authority" -ForegroundColor Cyan

Write-Host ""
Write-Host "=== Verification Complete ===" -ForegroundColor Green
